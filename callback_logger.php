<?php
/**
 * Callback Logger
 * 
 * This file logs all incoming requests to help debug callback issues.
 * Place this at: modules/gateways/callback/callback_logger.php
 */

$logFile = 'callback_debug.log';

// Get current timestamp
$timestamp = date('Y-m-d H:i:s');

// Collect all request data
$requestData = array(
    'timestamp' => $timestamp,
    'method' => $_SERVER['REQUEST_METHOD'],
    'url' => $_SERVER['REQUEST_URI'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? '',
    'get_data' => $_GET,
    'post_data' => $_POST,
    'headers' => getallheaders(),
    'raw_input' => file_get_contents('php://input')
);

// Log to file
$logEntry = "=== CALLBACK REQUEST ===\n";
$logEntry .= "Time: $timestamp\n";
$logEntry .= "Method: " . $_SERVER['REQUEST_METHOD'] . "\n";
$logEntry .= "URL: " . $_SERVER['REQUEST_URI'] . "\n";
$logEntry .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
$logEntry .= "Remote IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "\n";
$logEntry .= "GET Data: " . print_r($_GET, true) . "\n";
$logEntry .= "POST Data: " . print_r($_POST, true) . "\n";
$logEntry .= "Headers: " . print_r(getallheaders(), true) . "\n";
$logEntry .= "Raw Input: " . file_get_contents('php://input') . "\n";
$logEntry .= "========================\n\n";

file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

// Also log to error log
error_log("StorePay Callback Logger - Request received: " . print_r($requestData, true));

// Return success response
echo "SUCCESS";

// Display debug info if accessed via browser
if (isset($_GET['debug']) || empty($_GET)) {
    echo "<h1>StorePay Callback Logger</h1>";
    echo "<h2>Request Data</h2>";
    echo "<pre>" . htmlspecialchars(print_r($requestData, true)) . "</pre>";
    
    echo "<h2>Recent Log Entries</h2>";
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $logEntries = explode("=== CALLBACK REQUEST ===", $logContent);
        $recentEntries = array_slice($logEntries, -5); // Last 5 entries
        
        foreach ($recentEntries as $entry) {
            if (trim($entry)) {
                echo "<pre style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
                echo "=== CALLBACK REQUEST ===" . htmlspecialchars($entry);
                echo "</pre>";
            }
        }
    } else {
        echo "<p>No log file found yet.</p>";
    }
    
    echo "<h2>Instructions</h2>";
    echo "<ol>";
    echo "<li>Configure StorePay to send callbacks to: <code>" . $_SERVER['HTTP_HOST'] . "/modules/gateways/callback/callback_logger.php</code></li>";
    echo "<li>Make a test payment</li>";
    echo "<li>Check this page again to see if callbacks are being received</li>";
    echo "<li>Once callbacks are confirmed, switch back to the real callback file</li>";
    echo "</ol>";
}
?>
