<?php
/**
 * WHMCS StorePay Payment Gateway Module
 *
 * This module integrates WHMCS with StorePay payment gateway
 * supporting credit card payments through hosted checkout.
 *
 * <AUTHOR> Name
 * @copyright Copyright (c) 2025
 * @license MIT License
 * @version 1.0.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Define module related meta data.
 *
 * Values returned here are used to determine module related capabilities and
 * settings.
 *
 * @see https://developers.whmcs.com/payment-gateways/meta-data-params/
 *
 * @return array
 */
function storepay_MetaData()
{
    return array(
        'DisplayName' => 'StorePay Payment Gateway',
        'APIVersion' => '1.1', // Use API Version 1.1
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
    );
}

/**
 * Define gateway configuration options.
 *
 * The fields you define here determine the configuration options that are
 * presented to administrator users when activating and configuring your
 * payment gateway module for use.
 *
 * @return array
 */
function storepay_config()
{
    return array(
        // the friendly display name for a payment gateway should be
        // defined here for backwards compatibility
        'FriendlyName' => array(
            'Type' => 'System',
            'Value' => 'StorePay Payment Gateway',
        ),
        // Merchant ID
        'mchId' => array(
            'FriendlyName' => 'Merchant ID',
            'Type' => 'text',
            'Size' => '25',
            'Default' => '',
            'Description' => 'Enter your StorePay Merchant ID',
        ),
        // Application ID
        'appId' => array(
            'FriendlyName' => 'Application ID',
            'Type' => 'text',
            'Size' => '25',
            'Default' => '',
            'Description' => 'Enter your StorePay Application ID',
        ),
        // Secret Key
        'secretKey' => array(
            'FriendlyName' => 'Secret Key',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your StorePay Secret Key',
        ),
        // Test Mode
        'testMode' => array(
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Tick to enable test mode',
        ),
        // Product ID
        'productId' => array(
            'FriendlyName' => 'Product ID',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '8000',
            'Description' => 'Product ID (default: 8000 for credit card)',
        ),
    );
}

/**
 * Generate MD5 signature for StorePay API
 *
 * @param array $params Parameters to sign
 * @param string $key Secret key
 * @return string MD5 signature
 */
function storepay_generateSignature($params, $key)
{
    // Remove empty values and sign parameter
    $filteredParams = array();
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k !== 'sign') {
            $filteredParams[$k] = $v;
        }
    }
    
    // Sort parameters by key (ASCII order)
    ksort($filteredParams);
    
    // Build query string
    $queryString = '';
    foreach ($filteredParams as $k => $v) {
        if (is_array($v) || is_object($v)) {
            $v = json_encode($v, JSON_UNESCAPED_UNICODE);
        }
        $queryString .= $k . '=' . $v . '&';
    }
    
    // Remove trailing &
    $queryString = rtrim($queryString, '&');
    
    // Append key
    $stringToSign = $queryString . '&key=' . $key;
    
    // Generate MD5 hash and convert to uppercase
    return strtoupper(md5($stringToSign));
}

/**
 * Payment link.
 *
 * Required by third party payment gateway modules only.
 *
 * Defines the HTML output displayed on an invoice. Typically consists of an
 * HTML form that will take the user to the payment gateway endpoint.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @see https://developers.whmcs.com/payment-gateways/third-party-gateway/
 *
 * @return string
 */
function storepay_link($params)
{
    // Gateway Configuration Parameters
    $mchId = $params['mchId'];
    $appId = $params['appId'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];
    $productId = $params['productId'];

    // Invoice Parameters
    $invoiceId = $params['invoiceid'];
    $description = $params["description"];
    $amount = $params['amount'];
    $currencyCode = $params['currency'];

    // Client Parameters
    $firstname = $params['clientdetails']['firstname'];
    $lastname = $params['clientdetails']['lastname'];
    $email = $params['clientdetails']['email'];
    $address1 = $params['clientdetails']['address1'];
    $address2 = $params['clientdetails']['address2'];
    $city = $params['clientdetails']['city'];
    $state = $params['clientdetails']['state'];
    $postcode = $params['clientdetails']['postcode'];
    $country = $params['clientdetails']['country'];
    $phone = $params['clientdetails']['phonenumber'];

    // System Parameters
    $companyName = $params['companyname'];
    $systemUrl = $params['systemurl'];
    $returnUrl = $params['returnurl'];
    $langPayNow = $params['langpaynow'];
    $moduleDisplayName = $params['name'];
    $moduleName = $params['paymentmethod'];

    // Determine API URL based on test mode
    $apiUrl = $testMode ? 'https://test.storepay.cn/api/pay/create_order' : 'https://api.storepay.cn/api/pay/create_order';

    // Convert amount to cents (StorePay expects amount in cents)
    $amountInCents = intval($amount * 100);

    // Prepare extra data (customer information)
    $extra = array(
        'email' => $email,
        'shippingFirstName' => $firstname,
        'shippingLastName' => $lastname,
        'shippingAddress' => $address1 . ($address2 ? ' ' . $address2 : ''),
        'shippingCity' => $city,
        'shippingState' => $state,
        'shippingCountry' => $country,
        'shippingZipCode' => $postcode,
        'shippingPhone' => $phone,
        'billingFirstName' => $firstname,
        'billingLastName' => $lastname,
        'billingAddress' => $address1 . ($address2 ? ' ' . $address2 : ''),
        'billingCity' => $city,
        'billingState' => $state,
        'billingCountry' => $country,
        'billingZipCode' => $postcode,
        'billingPhone' => $phone,
    );

    // Prepare items data
    $items = array(
        array(
            'name' => $description,
            'category' => 'Service',
            'sku' => 'INV-' . $invoiceId,
            'url' => $systemUrl,
            'unitPrice' => $amount,
            'quantity' => 1,
        )
    );

    // Generate unique order number to avoid duplicates
    // Format: INV{invoiceId}_{timestamp}_{random}
    $uniqueOrderNo = 'INV' . $invoiceId . '_' . time() . '_' . rand(1000, 9999);

    // Prepare form data
    $postfields = array(
        'mchId' => $mchId,
        'appId' => $appId,
        'productId' => $productId,
        'mchOrderNo' => $uniqueOrderNo,
        'currency' => $currencyCode,
        'amount' => $amountInCents,
        'returnUrl' => $returnUrl,
        'notifyUrl' => $systemUrl . '/modules/gateways/callback/' . $moduleName . '.php',
        'items' => json_encode($items, JSON_UNESCAPED_UNICODE),
        'extra' => json_encode($extra, JSON_UNESCAPED_UNICODE),
    );

    // Generate signature
    $postfields['sign'] = storepay_generateSignature($postfields, $secretKey);

    // Make API call to create order and get payment URL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postfields));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false || $httpCode !== 200) {
        return '<div class="alert alert-danger">Payment gateway error: Unable to connect to payment service. Please try again later.</div>';
    }

    $responseData = json_decode($response, true);

    if (!$responseData) {
        return '<div class="alert alert-danger">Payment gateway error: Invalid response from payment service. Please try again later.</div>';
    }

    // Check if order creation was successful
    if ($responseData['retCode'] !== 'SUCCESS') {
        $errorMsg = $responseData['retMsg'] ?? 'Unknown error';
        return '<div class="alert alert-danger">Payment gateway error: ' . htmlspecialchars($errorMsg) . '</div>';
    }

    // Extract payment URL from response
    if (!isset($responseData['payParams']['payUrl'])) {
        return '<div class="alert alert-danger">Payment gateway error: No payment URL received. Please try again later.</div>';
    }

    $paymentUrl = $responseData['payParams']['payUrl'];
    $payOrderId = $responseData['payOrderId'] ?? '';

    // Log the successful order creation
    logTransaction($moduleDisplayName, array(
        'action' => 'create_order',
        'invoice_id' => $invoiceId,
        'unique_order_no' => $uniqueOrderNo,
        'pay_order_id' => $payOrderId,
        'amount' => $amount,
        'currency' => $currencyCode,
        'payment_url' => $paymentUrl
    ), 'Order Created Successfully');

    // Build HTML to redirect to payment URL
    $htmlOutput = '<div class="text-center">';
    $htmlOutput .= '<p>Redirecting to payment gateway...</p>';
    $htmlOutput .= '<a href="' . htmlspecialchars($paymentUrl) . '" class="btn btn-primary btn-lg">' . $langPayNow . '</a>';
    $htmlOutput .= '<script>window.location.href = "' . htmlspecialchars($paymentUrl) . '";</script>';
    $htmlOutput .= '</div>';

    return $htmlOutput;
}

/**
 * Refund transaction.
 *
 * Called when a refund is requested for a previously successful transaction.
 *
 * @param array $params Payment Gateway Module Parameters
 *
 * @see https://developers.whmcs.com/payment-gateways/refunds/
 *
 * @return array Transaction response status
 */
function storepay_refund($params)
{
    // Gateway Configuration Parameters
    $mchId = $params['mchId'];
    $appId = $params['appId'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];

    // Transaction Parameters
    $transactionIdToRefund = $params['transid'];
    $refundAmount = $params['amount'];
    $currencyCode = $params['currency'];
    $invoiceId = $params['invoiceid'];

    // Determine API URL based on test mode
    $apiUrl = $testMode ? 'https://test.storepay.cn/api/refund/create_order' : 'https://api.storepay.cn/api/refund/create_order';

    // Convert amount to cents
    $refundAmountInCents = intval($refundAmount * 100);

    // Generate unique refund order number
    $mchRefundNo = 'RF' . $invoiceId . '_' . time();

    // Prepare refund data
    $postData = array(
        'mchId' => $mchId,
        'appId' => $appId,
        'payOrderId' => $transactionIdToRefund,
        'mchOrderNo' => $invoiceId,
        'mchRefundNo' => $mchRefundNo,
        'refundAmount' => $refundAmountInCents,
        'currency' => $currencyCode,
        'refundReason' => 'Refund requested via WHMCS',
        'notifyUrl' => $params['systemurl'] . '/modules/gateways/callback/' . $params['paymentmethod'] . '.php',
    );

    // Generate signature
    $postData['sign'] = storepay_generateSignature($postData, $secretKey);

    // Make API call
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false || $httpCode !== 200) {
        return array(
            'status' => 'error',
            'rawdata' => 'HTTP Error: ' . $httpCode,
        );
    }

    $responseData = json_decode($response, true);

    if (!$responseData) {
        return array(
            'status' => 'error',
            'rawdata' => 'Invalid JSON response: ' . $response,
        );
    }

    // Check response status
    if ($responseData['retCode'] === 'SUCCESS') {
        return array(
            'status' => 'success',
            'rawdata' => $responseData,
            'transid' => $responseData['refundOrderId'],
        );
    } elseif ($responseData['retCode'] === 'PROCESSING') {
        return array(
            'status' => 'pending',
            'rawdata' => $responseData,
            'transid' => $responseData['refundOrderId'],
        );
    } else {
        return array(
            'status' => 'declined',
            'rawdata' => $responseData,
        );
    }
}

/**
 * Query transaction status.
 *
 * This function allows querying the current status of a transaction
 * from the StorePay payment gateway.
 *
 * @param array $params Payment Gateway Module Parameters
 * @return array Transaction status information
 */
function storepay_queryTransaction($params)
{
    // Gateway Configuration Parameters
    $mchId = $params['mchId'];
    $appId = $params['appId'];
    $secretKey = $params['secretKey'];
    $testMode = $params['testMode'];

    // Transaction Parameters
    $payOrderId = $params['transid'];
    $invoiceId = $params['invoiceid'];

    // Determine API URL based on test mode
    $apiUrl = $testMode ? 'https://test.storepay.cn/api/pay/query_order' : 'https://api.storepay.cn/api/pay/query_order';

    // Prepare query data
    $postData = array(
        'mchId' => $mchId,
        'appId' => $appId,
        'payOrderId' => $payOrderId,
        'mchOrderNo' => $invoiceId,
        'executeNotify' => false,
    );

    // Generate signature
    $postData['sign'] = storepay_generateSignature($postData, $secretKey);

    // Make API call
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false || $httpCode !== 200) {
        return array(
            'status' => 'error',
            'message' => 'HTTP Error: ' . $httpCode,
        );
    }

    $responseData = json_decode($response, true);

    if (!$responseData) {
        return array(
            'status' => 'error',
            'message' => 'Invalid JSON response: ' . $response,
        );
    }

    // Return transaction status information
    if ($responseData['retCode'] === 'SUCCESS') {
        $status = '';
        switch ($responseData['status']) {
            case 1:
                $status = 'pending';
                break;
            case 2:
                $status = 'success';
                break;
            case 4:
                $status = 'partially_refunded';
                break;
            case 5:
                $status = 'fully_refunded';
                break;
            case -1:
                $status = 'failed';
                break;
            default:
                $status = 'unknown';
                break;
        }

        return array(
            'status' => $status,
            'amount' => $responseData['amount'],
            'currency' => $responseData['currency'],
            'paymentMethod' => $responseData['paymentMethod'] ?? '',
            'paySuccTime' => $responseData['paySuccTime'] ?? '',
            'rawdata' => $responseData,
        );
    } else {
        return array(
            'status' => 'error',
            'message' => $responseData['retMsg'] ?? 'Unknown error',
            'rawdata' => $responseData,
        );
    }
}
