<?php
/**
 * WHMCS StorePay Payment Gateway Callback Handler
 *
 * This file handles payment notifications from StorePay payment gateway.
 * It validates the callback data, verifies the signature, and updates
 * the invoice payment status in WHMCS.
 *
 * <AUTHOR> Name
 * @copyright Copyright (c) 2025
 * @license MIT License
 * @version 1.0.0
 */

// Enhanced logging for debugging
$callbackStartTime = microtime(true);
$callbackLogFile = __DIR__ . '/storepay_callback.log';

function logCallbackDebug($message) {
    global $callbackLogFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    file_put_contents($callbackLogFile, $logEntry, FILE_APPEND | LOCK_EX);
    error_log("StorePay Callback: $message");
}

logCallbackDebug("=== CALLBACK START ===");
logCallbackDebug("Request Method: " . $_SERVER['REQUEST_METHOD']);
logCallbackDebug("Request URI: " . $_SERVER['REQUEST_URI']);
logCallbackDebug("User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'));
logCallbackDebug("Remote IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));
logCallbackDebug("GET Data: " . print_r($_GET, true));
logCallbackDebug("POST Data: " . print_r($_POST, true));

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

/**
 * Generate MD5 signature for StorePay API (same as in main module)
 *
 * @param array $params Parameters to sign
 * @param string $key Secret key
 * @return string MD5 signature
 */
function generateSignature($params, $key)
{
    // Remove empty values and sign parameter
    $filteredParams = array();
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k !== 'sign') {
            $filteredParams[$k] = $v;
        }
    }
    
    // Sort parameters by key (ASCII order)
    ksort($filteredParams);
    
    // Build query string
    $queryString = '';
    foreach ($filteredParams as $k => $v) {
        if (is_array($v) || is_object($v)) {
            $v = json_encode($v, JSON_UNESCAPED_UNICODE);
        }
        $queryString .= $k . '=' . $v . '&';
    }
    
    // Remove trailing &
    $queryString = rtrim($queryString, '&');
    
    // Append key
    $stringToSign = $queryString . '&key=' . $key;
    
    // Generate MD5 hash and convert to uppercase
    return strtoupper(md5($stringToSign));
}

// Retrieve data returned in payment gateway callback
// StorePay can send callbacks via GET or POST, so check both
$requestData = array_merge($_GET, $_POST);

$payOrderId = $requestData['payOrderId'] ?? '';
$mchId = $requestData['mchId'] ?? '';
$appId = $requestData['appId'] ?? '';
$mchOrderNo = $requestData['mchOrderNo'] ?? '';
$amount = $requestData['amount'] ?? '';
$currency = $requestData['currency'] ?? '';
$status = $requestData['status'] ?? '';
$paymentMethod = $requestData['paymentMethod'] ?? '';
$refundAmount = $requestData['refundAmount'] ?? '';
$paySuccTime = $requestData['paySuccTime'] ?? '';
$resultCode = $requestData['resultCode'] ?? '';
$resultMsg = $requestData['resultMsg'] ?? '';
$receivedSign = $requestData['sign'] ?? '';

// Debug: Log all received data
logCallbackDebug("Merged request data: " . print_r($requestData, true));

// Validate required parameters
if (empty($payOrderId) || empty($mchOrderNo) || empty($status) || empty($receivedSign)) {
    error_log("StorePay Callback - Missing required parameters");
    die("Missing required parameters");
}

// Verify signature
$calculatedSign = generateSignature($requestData, $gatewayParams['secretKey']);
error_log("StorePay Callback - Signature check: Expected=$calculatedSign, Received=$receivedSign");

if ($calculatedSign !== $receivedSign) {
    logTransaction($gatewayParams['name'], $requestData, "Signature Verification Failed - Expected: $calculatedSign, Received: $receivedSign");
    error_log("StorePay Callback - Signature verification failed");
    die("Signature verification failed");
}

// Extract invoice ID from order number
// Order number format: INV{invoiceId}_{timestamp}_{random}
$invoiceId = null;
if (preg_match('/^INV(\d+)_\d+_\d+$/', $mchOrderNo, $matches)) {
    $invoiceId = $matches[1];
} else {
    // Fallback: assume the order number is the invoice ID directly (for backward compatibility)
    $invoiceId = $mchOrderNo;
}

error_log("StorePay Callback - Extracted invoice ID: $invoiceId from order: $mchOrderNo");

// Validate invoice ID
try {
    $invoiceId = checkCbInvoiceID($invoiceId, $gatewayParams['name']);
    error_log("StorePay Callback - Invoice ID validated: $invoiceId");
} catch (Exception $e) {
    error_log("StorePay Callback - Invoice validation failed: " . $e->getMessage());
    logTransaction($gatewayParams['name'], $requestData, "Invoice Validation Failed: " . $e->getMessage());
    die("Invoice validation failed");
}

// Check transaction ID hasn't been processed before
error_log("StorePay Callback - Checking transaction ID: $payOrderId");
try {
    checkCbTransID($payOrderId);
    error_log("StorePay Callback - Transaction ID validated");
} catch (Exception $e) {
    error_log("StorePay Callback - Transaction already processed: " . $e->getMessage());
    logTransaction($gatewayParams['name'], $requestData, "Duplicate Transaction: " . $e->getMessage());
    // If it's a duplicate, still return SUCCESS to acknowledge receipt
    echo "SUCCESS";
    exit;
}

// Determine transaction status
$transactionStatus = "Unknown";
$success = false;

// Convert status to integer for comparison
$statusInt = intval($status);
error_log("StorePay Callback - Status: '$status' (converted to int: $statusInt)");

switch ($statusInt) {
    case 1:
        $transactionStatus = "Payment Pending";
        break;
    case 2:
        $transactionStatus = "Payment Successful";
        $success = true;
        break;
    case 4:
        $transactionStatus = "Partially Refunded";
        $success = true;
        break;
    case 5:
        $transactionStatus = "Fully Refunded";
        $success = true;
        break;
    case -1:
        $transactionStatus = "Payment Failed";
        break;
    default:
        $transactionStatus = "Unknown Status: " . $status;
        break;
}

error_log("StorePay Callback - Transaction status: $transactionStatus, Success: " . ($success ? 'true' : 'false'));

// Log the transaction
logTransaction($gatewayParams['name'], $requestData, $transactionStatus);

// Process successful payments
if ($success && $statusInt == 2) {
    error_log("StorePay Callback - Processing successful payment");

    // Convert amount from yuan to the invoice currency if needed
    $paymentAmount = floatval($amount);
    error_log("StorePay Callback - Payment amount: $paymentAmount");

    try {
        // Add payment to invoice
        $result = addInvoicePayment(
            $invoiceId,
            $payOrderId,
            $paymentAmount,
            0, // No fee information available
            $gatewayModuleName
        );

        error_log("StorePay Callback - addInvoicePayment result: " . print_r($result, true));

        if ($result === true || $result === 'success') {
            error_log("StorePay Callback - Payment added successfully");
            logTransaction($gatewayParams['name'], $requestData, "Payment Added Successfully");
        } else {
            error_log("StorePay Callback - Payment addition failed: " . print_r($result, true));
            logTransaction($gatewayParams['name'], $requestData, "Payment Addition Failed: " . print_r($result, true));
        }
    } catch (Exception $e) {
        error_log("StorePay Callback - Exception in addInvoicePayment: " . $e->getMessage());
        logTransaction($gatewayParams['name'], $requestData, "Payment Addition Exception: " . $e->getMessage());
    }

    // Send success response to StorePay
    echo "SUCCESS";
} elseif ($statusInt == 1) {
    // Payment is still pending, just log it
    error_log("StorePay Callback - Payment pending");
    echo "SUCCESS";
} else {
    // Payment failed or other status
    error_log("StorePay Callback - Payment failed or unknown status: $status");
    echo "SUCCESS"; // Still acknowledge receipt
}

// Optional: Handle refund notifications
if (isset($requestData['refundStatus'])) {
    $refundStatus = $requestData['refundStatus'];
    $refundOrderId = $requestData['refundOrderId'] ?? '';

    switch ($refundStatus) {
        case 0:
            $refundTransactionStatus = "Refund Failed";
            break;
        case 1:
            $refundTransactionStatus = "Refund Successful";
            break;
        case 2:
            $refundTransactionStatus = "Refund Processing";
            break;
        default:
            $refundTransactionStatus = "Unknown Refund Status: " . $refundStatus;
            break;
    }

    // Log refund transaction
    logTransaction($gatewayParams['name'], $requestData, $refundTransactionStatus);
}

// Log callback completion
$callbackEndTime = microtime(true);
$executionTime = round(($callbackEndTime - $callbackStartTime) * 1000, 2);
logCallbackDebug("=== CALLBACK END === (Execution time: {$executionTime}ms)");
logCallbackDebug("");
